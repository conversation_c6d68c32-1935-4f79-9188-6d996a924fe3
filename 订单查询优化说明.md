# 订单查询功能优化说明

## 优化内容

### 1. 智能重复查询检测
- **问题**: 用户反复发送查订单，查到的都是订单卡片的订单
- **解决方案**: 
  - 添加会话状态管理 `sessionOrderStates`
  - 检测用户在1分钟内重复查询同一订单卡片
  - 自动引导用户提供下单手机号码

### 2. 智能引导流程
- **主要流程**: 手机号 → 订单号 → 错误提示
- **引导逻辑**:
  1. 检测到重复查询订单卡片时，引导用户提供手机号
  2. 如果手机号格式不正确，引导用户提供订单号
  3. 如果订单号格式不正确，提示用户确认或截图

### 3. 自动触发订单查询
- **问题**: 用户发送订单号后，没有自动触发查订单功能
- **解决方案**: 
  - 添加 `queryOrderDetailById` 函数
  - 用户提供订单号后自动调用订单详情查询
  - 返回详细的订单状态和物流信息

## 技术实现

### 会话状态管理
```javascript
let sessionOrderStates = new Map(); // sessionId -> { 
    lastOrderQuery: timestamp, 
    orderCardShown: boolean, 
    waitingForPhone: boolean, 
    waitingForOrderId: boolean 
}
```

### 重复查询检测
```javascript
if (sessionState.orderCardShown && (now - sessionState.lastOrderQuery < 60000)) {
    // 引导用户提供手机号
    sessionState.waitingForPhone = true;
    // 发送引导消息
}
```

### 状态驱动的消息处理
- `waitingForPhone`: 等待用户提供手机号状态
- `waitingForOrderId`: 等待用户提供订单号状态
- 根据不同状态处理用户输入

### 订单号查询功能
- 使用 `GM_xmlhttpRequest` 请求订单详情页面
- 解析订单状态、物流信息
- 根据订单状态提供相应建议

## 用户体验改进

### 智能引导对话流程
1. **重复查询检测**:
   ```
   用户: 查订单 (重复)
   系统: 亲，我看到您多次查询这个订单，为了更好地为您服务，请提供一下您下单时使用的手机号码，我来帮您查询更详细的订单信息哦～
   ```

2. **手机号验证**:
   ```
   用户: 1234567890
   系统: 收到您的手机尾号【7890】，正在为您查询订单，请稍候...
   ```

3. **订单号备选方案**:
   ```
   用户: 12345 (格式错误)
   系统: 亲，手机号格式好像不太对呢，麻烦您提供一下订单号，我来帮您查询订单信息～
   ```

4. **订单号自动查询**:
   ```
   用户: 3729155716126225920
   系统: 收到您的订单号【3729155716126225920】，正在为您查询订单详情，请稍候...
   系统: 亲，您好！查询到订单【3729155716126225920】的详情如下：...
   ```

## 关键改进点

1. **状态持久化**: 会话状态在用户交互过程中保持
2. **智能识别**: 自动识别手机号和订单号格式
3. **流程完整**: 从重复查询检测到最终订单详情查询的完整流程
4. **用户友好**: 清晰的引导语言和错误提示
5. **自动化**: 减少人工干预，提高查询效率

## 测试场景

### 场景1: 重复查询订单卡片
1. 用户发送"查订单" - 显示订单卡片
2. 用户再次发送"查订单" - 触发引导提供手机号

### 场景2: 手机号查询流程
1. 用户提供正确手机号 - 自动查询
2. 用户提供错误手机号 - 引导提供订单号

### 场景3: 订单号查询流程
1. 用户提供正确订单号 - 自动查询订单详情
2. 用户提供错误订单号 - 提示确认或截图

这些优化确保了用户查询订单的完整体验，解决了重复查询和订单号查询不自动触发的问题。
